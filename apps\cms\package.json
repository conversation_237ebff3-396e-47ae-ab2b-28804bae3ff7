{"name": "@encreasl/cms", "version": "0.1.0", "description": "Encreasl CMS powered by PayloadCMS 3.0", "license": "MIT", "type": "module", "private": true, "scripts": {"build": "cross-env NODE_OPTIONS=\"--no-deprecation --max-old-space-size=8000\" next build", "dev": "cross-env NODE_OPTIONS=--no-deprecation next dev -p 3001", "devsafe": "rm -rf .next && cross-env NODE_OPTIONS=--no-deprecation next dev", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "lint": "cross-env NODE_OPTIONS=--no-deprecation next lint", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "start": "cross-env NODE_OPTIONS=--no-deprecation next start", "test": "pnpm run test:int && pnpm run test:e2e", "test:e2e": "cross-env NODE_OPTIONS=\"--no-deprecation --no-experimental-strip-types\" pnpm exec playwright test", "test:int": "cross-env NODE_OPTIONS=--no-deprecation vitest run --config ./vitest.config.mts", "db:test": "node test-db-connection.js"}, "dependencies": {"@encreasl/redux": "workspace:*", "@payloadcms/db-postgres": "3.49.0", "@payloadcms/next": "3.49.0", "@payloadcms/payload-cloud": "3.49.0", "@payloadcms/plugin-cloud-storage": "^3.52.0", "@payloadcms/richtext-lexical": "3.49.0", "@payloadcms/ui": "3.49.0", "cloudinary": "^2.7.0", "cross-env": "^7.0.3", "dotenv": "16.4.7", "firebase-admin": "^13.4.0", "graphql": "^16.8.1", "next": "15.4.2", "payload": "3.49.0", "pg": "^8.16.3", "react": "19.1.0", "react-dom": "19.1.0", "sharp": "0.34.2"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.31.0", "@playwright/test": "1.50.0", "@testing-library/react": "16.3.0", "@types/node": "^22.5.4", "@types/react": "19.1.0", "@types/react-dom": "19.1.2", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-react": "4.5.2", "eslint": "^9.16.0", "eslint-config-next": "15.4.2", "jsdom": "26.1.0", "playwright": "1.50.0", "playwright-core": "1.50.0", "prettier": "^3.4.2", "typescript": "5.7.3", "vite-tsconfig-paths": "5.1.4", "vitest": "3.2.3"}, "engines": {"node": "^18.20.2 || >=20.9.0", "pnpm": "^9 || ^10"}, "pnpm": {"onlyBuiltDependencies": ["sharp", "esbuild", "unrs-resolver"]}}