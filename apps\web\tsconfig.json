{"extends": "@encreasl/typescript-config/nextjs.json", "compilerOptions": {"target": "es2015", "skipLibCheck": true, "noImplicitAny": false, "strict": false, "noImplicitReturns": false, "noUnusedLocals": false, "noUnusedParameters": false, "jsx": "preserve", "paths": {"@/*": ["./src/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/types/**/*.ts"], "exclude": ["node_modules"]}