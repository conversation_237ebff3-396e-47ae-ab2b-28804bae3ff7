{"extends": "@encreasl/typescript-config/nextjs.json", "compilerOptions": {"target": "es2015", "skipLibCheck": true, "noImplicitAny": false, "strict": false, "jsx": "preserve", "jsxImportSource": "react", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "paths": {"@/*": ["./src/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}