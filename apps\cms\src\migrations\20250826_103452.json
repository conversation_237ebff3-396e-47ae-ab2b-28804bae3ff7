{"id": "301b57da-c7b9-4e06-9460-5acaa3376c57", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.users_sessions": {"name": "users_sessions", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true}}, "indexes": {"users_sessions_order_idx": {"name": "users_sessions_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_sessions_parent_id_idx": {"name": "users_sessions_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"users_sessions_parent_id_fk": {"name": "users_sessions_parent_id_fk", "tableFrom": "users_sessions", "tableTo": "users", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "middle_name": {"name": "middle_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "name_extension": {"name": "name_extension", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "enum_users_role", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'trainee'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "bio": {"name": "bio", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "profile_image_url": {"name": "profile_image_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "enum_users_gender", "typeSchema": "public", "primaryKey": false, "notNull": false}, "civil_status": {"name": "civil_status", "type": "enum_users_civil_status", "typeSchema": "public", "primaryKey": false, "notNull": false}, "nationality": {"name": "nationality", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "birth_date": {"name": "birth_date", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "place_of_birth": {"name": "place_of_birth", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "complete_address": {"name": "complete_address", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "preferences": {"name": "preferences", "type": "jsonb", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "last_login": {"name": "last_login", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "reset_password_token": {"name": "reset_password_token", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "reset_password_expiration": {"name": "reset_password_expiration", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "salt": {"name": "salt", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "hash": {"name": "hash", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "login_attempts": {"name": "login_attempts", "type": "numeric", "primaryKey": false, "notNull": false, "default": 0}, "lock_until": {"name": "lock_until", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"users_username_idx": {"name": "users_username_idx", "columns": [{"expression": "username", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "users_updated_at_idx": {"name": "users_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_created_at_idx": {"name": "users_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_email_idx": {"name": "users_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.instructors": {"name": "instructors", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "specialization": {"name": "specialization", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "years_experience": {"name": "years_experience", "type": "numeric", "primaryKey": false, "notNull": false}, "certifications": {"name": "certifications", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "office_hours": {"name": "office_hours", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "contact_email": {"name": "contact_email", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "teaching_permissions": {"name": "teaching_permissions", "type": "jsonb", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"instructors_user_idx": {"name": "instructors_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "instructors_updated_at_idx": {"name": "instructors_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "instructors_created_at_idx": {"name": "instructors_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"instructors_user_id_users_id_fk": {"name": "instructors_user_id_users_id_fk", "tableFrom": "instructors", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.trainees": {"name": "trainees", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "srn": {"name": "srn", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "enrollment_date": {"name": "enrollment_date", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "current_level": {"name": "current_level", "type": "enum_trainees_current_level", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'beginner'"}, "graduation_target_date": {"name": "graduation_target_date", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "learning_path": {"name": "learning_path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "coupon_code": {"name": "coupon_code", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"trainees_user_idx": {"name": "trainees_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "trainees_srn_idx": {"name": "trainees_srn_idx", "columns": [{"expression": "srn", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "trainees_updated_at_idx": {"name": "trainees_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "trainees_created_at_idx": {"name": "trainees_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"trainees_user_id_users_id_fk": {"name": "trainees_user_id_users_id_fk", "tableFrom": "trainees", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.admins_department_access": {"name": "admins_department_access", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "department": {"name": "department", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"admins_department_access_order_idx": {"name": "admins_department_access_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "admins_department_access_parent_id_idx": {"name": "admins_department_access_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"admins_department_access_parent_id_fk": {"name": "admins_department_access_parent_id_fk", "tableFrom": "admins_department_access", "tableTo": "admins", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.admins": {"name": "admins", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "admin_level": {"name": "admin_level", "type": "enum_admins_admin_level", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'content'"}, "system_permissions": {"name": "system_permissions", "type": "jsonb", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"admins_user_idx": {"name": "admins_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "admins_updated_at_idx": {"name": "admins_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "admins_created_at_idx": {"name": "admins_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"admins_user_id_users_id_fk": {"name": "admins_user_id_users_id_fk", "tableFrom": "admins", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_certifications": {"name": "user_certifications", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "certification_name": {"name": "certification_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "issuing_authority": {"name": "issuing_authority", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "issue_date": {"name": "issue_date", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "expiry_date": {"name": "expiry_date", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "verification_url": {"name": "verification_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"user_certifications_user_idx": {"name": "user_certifications_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_certifications_updated_at_idx": {"name": "user_certifications_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_certifications_created_at_idx": {"name": "user_certifications_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_certifications_user_id_users_id_fk": {"name": "user_certifications_user_id_users_id_fk", "tableFrom": "user_certifications", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_relationships": {"name": "user_relationships", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "related_entity_type": {"name": "related_entity_type", "type": "enum_user_relationships_related_entity_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "related_entity_id": {"name": "related_entity_id", "type": "numeric", "primaryKey": false, "notNull": true}, "relationship_type": {"name": "relationship_type", "type": "enum_user_relationships_relationship_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "relationship_data": {"name": "relationship_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"user_relationships_user_idx": {"name": "user_relationships_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_relationships_updated_at_idx": {"name": "user_relationships_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_relationships_created_at_idx": {"name": "user_relationships_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_relationships_user_id_users_id_fk": {"name": "user_relationships_user_id_users_id_fk", "tableFrom": "user_relationships", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_events": {"name": "user_events", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "event_type": {"name": "event_type", "type": "enum_user_events_event_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "event_data": {"name": "event_data", "type": "jsonb", "primaryKey": false, "notNull": true}, "triggered_by_id": {"name": "triggered_by_id", "type": "integer", "primaryKey": false, "notNull": false}, "timestamp": {"name": "timestamp", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"user_events_user_idx": {"name": "user_events_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_events_triggered_by_idx": {"name": "user_events_triggered_by_idx", "columns": [{"expression": "triggered_by_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_events_updated_at_idx": {"name": "user_events_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_events_created_at_idx": {"name": "user_events_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_events_user_id_users_id_fk": {"name": "user_events_user_id_users_id_fk", "tableFrom": "user_events", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "user_events_triggered_by_id_users_id_fk": {"name": "user_events_triggered_by_id_users_id_fk", "tableFrom": "user_events", "tableTo": "users", "columnsFrom": ["triggered_by_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.emergency_contacts": {"name": "emergency_contacts", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "middle_name": {"name": "middle_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "contact_number": {"name": "contact_number", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "relationship": {"name": "relationship", "type": "enum_emergency_contacts_relationship", "typeSchema": "public", "primaryKey": false, "notNull": true}, "complete_address": {"name": "complete_address", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "is_primary": {"name": "is_primary", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"emergency_contacts_user_idx": {"name": "emergency_contacts_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "emergency_contacts_updated_at_idx": {"name": "emergency_contacts_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "emergency_contacts_created_at_idx": {"name": "emergency_contacts_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"emergency_contacts_user_id_users_id_fk": {"name": "emergency_contacts_user_id_users_id_fk", "tableFrom": "emergency_contacts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.media": {"name": "media", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "alt": {"name": "alt", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "cloudinary_public_id": {"name": "cloudinary_public_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "cloudinary_u_r_l": {"name": "cloudinary_u_r_l", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "thumbnail_u_r_l": {"name": "thumbnail_u_r_l", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "filename": {"name": "filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "mime_type": {"name": "mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "filesize": {"name": "filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "width": {"name": "width", "type": "numeric", "primaryKey": false, "notNull": false}, "height": {"name": "height", "type": "numeric", "primaryKey": false, "notNull": false}, "focal_x": {"name": "focal_x", "type": "numeric", "primaryKey": false, "notNull": false}, "focal_y": {"name": "focal_y", "type": "numeric", "primaryKey": false, "notNull": false}}, "indexes": {"media_updated_at_idx": {"name": "media_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_created_at_idx": {"name": "media_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_filename_idx": {"name": "media_filename_idx", "columns": [{"expression": "filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.posts_tags": {"name": "posts_tags", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "tag": {"name": "tag", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"posts_tags_order_idx": {"name": "posts_tags_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_tags_parent_id_idx": {"name": "posts_tags_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"posts_tags_parent_id_fk": {"name": "posts_tags_parent_id_fk", "tableFrom": "posts_tags", "tableTo": "posts", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.posts": {"name": "posts", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "jsonb", "primaryKey": false, "notNull": false}, "excerpt": {"name": "excerpt", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "featured_image_id": {"name": "featured_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "enum_posts_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'draft'"}, "published_at": {"name": "published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "author_id": {"name": "author_id", "type": "integer", "primaryKey": false, "notNull": false}, "seo_title": {"name": "seo_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "seo_description": {"name": "seo_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "seo_focus_keyword": {"name": "seo_focus_keyword", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "_status": {"name": "_status", "type": "enum_posts_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'draft'"}}, "indexes": {"posts_slug_idx": {"name": "posts_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "posts_featured_image_idx": {"name": "posts_featured_image_idx", "columns": [{"expression": "featured_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_author_idx": {"name": "posts_author_idx", "columns": [{"expression": "author_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_updated_at_idx": {"name": "posts_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_created_at_idx": {"name": "posts_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts__status_idx": {"name": "posts__status_idx", "columns": [{"expression": "_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"posts_featured_image_id_media_id_fk": {"name": "posts_featured_image_id_media_id_fk", "tableFrom": "posts", "tableTo": "media", "columnsFrom": ["featured_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "posts_author_id_users_id_fk": {"name": "posts_author_id_users_id_fk", "tableFrom": "posts", "tableTo": "users", "columnsFrom": ["author_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._posts_v_version_tags": {"name": "_posts_v_version_tags", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "tag": {"name": "tag", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_posts_v_version_tags_order_idx": {"name": "_posts_v_version_tags_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_tags_parent_id_idx": {"name": "_posts_v_version_tags_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_posts_v_version_tags_parent_id_fk": {"name": "_posts_v_version_tags_parent_id_fk", "tableFrom": "_posts_v_version_tags", "tableTo": "_posts_v", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._posts_v": {"name": "_posts_v", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_title": {"name": "version_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_slug": {"name": "version_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_content": {"name": "version_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "version_excerpt": {"name": "version_excerpt", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_featured_image_id": {"name": "version_featured_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_status": {"name": "version_status", "type": "enum__posts_v_version_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'draft'"}, "version_published_at": {"name": "version_published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_author_id": {"name": "version_author_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_seo_title": {"name": "version_seo_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_seo_description": {"name": "version_seo_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_seo_focus_keyword": {"name": "version_seo_focus_keyword", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_updated_at": {"name": "version_updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_created_at": {"name": "version_created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version__status": {"name": "version__status", "type": "enum__posts_v_version_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'draft'"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "latest": {"name": "latest", "type": "boolean", "primaryKey": false, "notNull": false}}, "indexes": {"_posts_v_parent_idx": {"name": "_posts_v_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_version_slug_idx": {"name": "_posts_v_version_version_slug_idx", "columns": [{"expression": "version_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_version_featured_image_idx": {"name": "_posts_v_version_version_featured_image_idx", "columns": [{"expression": "version_featured_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_version_author_idx": {"name": "_posts_v_version_version_author_idx", "columns": [{"expression": "version_author_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_version_updated_at_idx": {"name": "_posts_v_version_version_updated_at_idx", "columns": [{"expression": "version_updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_version_created_at_idx": {"name": "_posts_v_version_version_created_at_idx", "columns": [{"expression": "version_created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_version__status_idx": {"name": "_posts_v_version_version__status_idx", "columns": [{"expression": "version__status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_created_at_idx": {"name": "_posts_v_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_updated_at_idx": {"name": "_posts_v_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_latest_idx": {"name": "_posts_v_latest_idx", "columns": [{"expression": "latest", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_posts_v_parent_id_posts_id_fk": {"name": "_posts_v_parent_id_posts_id_fk", "tableFrom": "_posts_v", "tableTo": "posts", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_posts_v_version_featured_image_id_media_id_fk": {"name": "_posts_v_version_featured_image_id_media_id_fk", "tableFrom": "_posts_v", "tableTo": "media", "columnsFrom": ["version_featured_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_posts_v_version_author_id_users_id_fk": {"name": "_posts_v_version_author_id_users_id_fk", "tableFrom": "_posts_v", "tableTo": "users", "columnsFrom": ["version_author_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.services_tags": {"name": "services_tags", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "tag": {"name": "tag", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {"services_tags_order_idx": {"name": "services_tags_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "services_tags_parent_id_idx": {"name": "services_tags_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"services_tags_parent_id_fk": {"name": "services_tags_parent_id_fk", "tableFrom": "services_tags", "tableTo": "services", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.services": {"name": "services", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "jsonb", "primaryKey": false, "notNull": true}, "excerpt": {"name": "excerpt", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "featured_image_id": {"name": "featured_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "enum_services_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'draft'"}, "published_at": {"name": "published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "author_id": {"name": "author_id", "type": "integer", "primaryKey": false, "notNull": true}, "seo_title": {"name": "seo_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "seo_description": {"name": "seo_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "seo_focus_keyword": {"name": "seo_focus_keyword", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"services_slug_idx": {"name": "services_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "services_featured_image_idx": {"name": "services_featured_image_idx", "columns": [{"expression": "featured_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "services_author_idx": {"name": "services_author_idx", "columns": [{"expression": "author_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "services_updated_at_idx": {"name": "services_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "services_created_at_idx": {"name": "services_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"services_featured_image_id_media_id_fk": {"name": "services_featured_image_id_media_id_fk", "tableFrom": "services", "tableTo": "media", "columnsFrom": ["featured_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "services_author_id_users_id_fk": {"name": "services_author_id_users_id_fk", "tableFrom": "services", "tableTo": "users", "columnsFrom": ["author_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_locked_documents": {"name": "payload_locked_documents", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "global_slug": {"name": "global_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_locked_documents_global_slug_idx": {"name": "payload_locked_documents_global_slug_idx", "columns": [{"expression": "global_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_updated_at_idx": {"name": "payload_locked_documents_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_created_at_idx": {"name": "payload_locked_documents_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_locked_documents_rels": {"name": "payload_locked_documents_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "users_id": {"name": "users_id", "type": "integer", "primaryKey": false, "notNull": false}, "instructors_id": {"name": "instructors_id", "type": "integer", "primaryKey": false, "notNull": false}, "trainees_id": {"name": "trainees_id", "type": "integer", "primaryKey": false, "notNull": false}, "admins_id": {"name": "admins_id", "type": "integer", "primaryKey": false, "notNull": false}, "user_certifications_id": {"name": "user_certifications_id", "type": "integer", "primaryKey": false, "notNull": false}, "user_relationships_id": {"name": "user_relationships_id", "type": "integer", "primaryKey": false, "notNull": false}, "user_events_id": {"name": "user_events_id", "type": "integer", "primaryKey": false, "notNull": false}, "emergency_contacts_id": {"name": "emergency_contacts_id", "type": "integer", "primaryKey": false, "notNull": false}, "media_id": {"name": "media_id", "type": "integer", "primaryKey": false, "notNull": false}, "posts_id": {"name": "posts_id", "type": "integer", "primaryKey": false, "notNull": false}, "services_id": {"name": "services_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"payload_locked_documents_rels_order_idx": {"name": "payload_locked_documents_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_parent_idx": {"name": "payload_locked_documents_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_path_idx": {"name": "payload_locked_documents_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_users_id_idx": {"name": "payload_locked_documents_rels_users_id_idx", "columns": [{"expression": "users_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_instructors_id_idx": {"name": "payload_locked_documents_rels_instructors_id_idx", "columns": [{"expression": "instructors_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_trainees_id_idx": {"name": "payload_locked_documents_rels_trainees_id_idx", "columns": [{"expression": "trainees_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_admins_id_idx": {"name": "payload_locked_documents_rels_admins_id_idx", "columns": [{"expression": "admins_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_user_certifications_id_idx": {"name": "payload_locked_documents_rels_user_certifications_id_idx", "columns": [{"expression": "user_certifications_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_user_relationships_id_idx": {"name": "payload_locked_documents_rels_user_relationships_id_idx", "columns": [{"expression": "user_relationships_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_user_events_id_idx": {"name": "payload_locked_documents_rels_user_events_id_idx", "columns": [{"expression": "user_events_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_emergency_contacts_id_idx": {"name": "payload_locked_documents_rels_emergency_contacts_id_idx", "columns": [{"expression": "emergency_contacts_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_media_id_idx": {"name": "payload_locked_documents_rels_media_id_idx", "columns": [{"expression": "media_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_posts_id_idx": {"name": "payload_locked_documents_rels_posts_id_idx", "columns": [{"expression": "posts_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_services_id_idx": {"name": "payload_locked_documents_rels_services_id_idx", "columns": [{"expression": "services_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payload_locked_documents_rels_parent_fk": {"name": "payload_locked_documents_rels_parent_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "payload_locked_documents", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_users_fk": {"name": "payload_locked_documents_rels_users_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "users", "columnsFrom": ["users_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_instructors_fk": {"name": "payload_locked_documents_rels_instructors_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "instructors", "columnsFrom": ["instructors_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_trainees_fk": {"name": "payload_locked_documents_rels_trainees_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "trainees", "columnsFrom": ["trainees_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_admins_fk": {"name": "payload_locked_documents_rels_admins_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "admins", "columnsFrom": ["admins_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_user_certifications_fk": {"name": "payload_locked_documents_rels_user_certifications_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "user_certifications", "columnsFrom": ["user_certifications_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_user_relationships_fk": {"name": "payload_locked_documents_rels_user_relationships_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "user_relationships", "columnsFrom": ["user_relationships_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_user_events_fk": {"name": "payload_locked_documents_rels_user_events_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "user_events", "columnsFrom": ["user_events_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_emergency_contacts_fk": {"name": "payload_locked_documents_rels_emergency_contacts_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "emergency_contacts", "columnsFrom": ["emergency_contacts_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_media_fk": {"name": "payload_locked_documents_rels_media_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "media", "columnsFrom": ["media_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_posts_fk": {"name": "payload_locked_documents_rels_posts_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "posts", "columnsFrom": ["posts_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_services_fk": {"name": "payload_locked_documents_rels_services_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "services", "columnsFrom": ["services_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_preferences": {"name": "payload_preferences", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "value": {"name": "value", "type": "jsonb", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_preferences_key_idx": {"name": "payload_preferences_key_idx", "columns": [{"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_updated_at_idx": {"name": "payload_preferences_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_created_at_idx": {"name": "payload_preferences_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_preferences_rels": {"name": "payload_preferences_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "users_id": {"name": "users_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"payload_preferences_rels_order_idx": {"name": "payload_preferences_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_parent_idx": {"name": "payload_preferences_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_path_idx": {"name": "payload_preferences_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_users_id_idx": {"name": "payload_preferences_rels_users_id_idx", "columns": [{"expression": "users_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payload_preferences_rels_parent_fk": {"name": "payload_preferences_rels_parent_fk", "tableFrom": "payload_preferences_rels", "tableTo": "payload_preferences", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_preferences_rels_users_fk": {"name": "payload_preferences_rels_users_fk", "tableFrom": "payload_preferences_rels", "tableTo": "users", "columnsFrom": ["users_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_migrations": {"name": "payload_migrations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "batch": {"name": "batch", "type": "numeric", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_migrations_updated_at_idx": {"name": "payload_migrations_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_migrations_created_at_idx": {"name": "payload_migrations_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.enum_users_role": {"name": "enum_users_role", "schema": "public", "values": ["admin", "instructor", "trainee"]}, "public.enum_users_gender": {"name": "enum_users_gender", "schema": "public", "values": ["male", "female", "other", "prefer_not_to_say"]}, "public.enum_users_civil_status": {"name": "enum_users_civil_status", "schema": "public", "values": ["single", "married", "divorced", "widowed", "separated"]}, "public.enum_trainees_current_level": {"name": "enum_trainees_current_level", "schema": "public", "values": ["beginner", "intermediate", "advanced"]}, "public.enum_admins_admin_level": {"name": "enum_admins_admin_level", "schema": "public", "values": ["system", "department", "content"]}, "public.enum_user_relationships_related_entity_type": {"name": "enum_user_relationships_related_entity_type", "schema": "public", "values": ["course", "department", "project", "group"]}, "public.enum_user_relationships_relationship_type": {"name": "enum_user_relationships_relationship_type", "schema": "public", "values": ["enrolled", "teaching", "managing", "supervising", "member"]}, "public.enum_user_events_event_type": {"name": "enum_user_events_event_type", "schema": "public", "values": ["USER_CREATED", "ROLE_CHANGED", "PROFILE_UPDATED", "USER_DEACTIVATED", "USER_REACTIVATED", "LOGIN_SUCCESS", "LOGIN_FAILED", "PASSWORD_CHANGED"]}, "public.enum_emergency_contacts_relationship": {"name": "enum_emergency_contacts_relationship", "schema": "public", "values": ["parent", "spouse", "sibling", "child", "guardian", "friend", "relative", "other"]}, "public.enum_posts_status": {"name": "enum_posts_status", "schema": "public", "values": ["draft", "published"]}, "public.enum__posts_v_version_status": {"name": "enum__posts_v_version_status", "schema": "public", "values": ["draft", "published"]}, "public.enum_services_status": {"name": "enum_services_status", "schema": "public", "values": ["draft", "published"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}}