/**
 * @file apps/web/src/components/auth/PublicRoute.tsx
 * @description Public route component for authentication pages
 * Redirects authenticated users away from auth pages (signin, register)
 */

'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useRouteProtection } from '@/hooks/useAuth';
import type { PublicRouteProps } from '@/types/auth';



// ========================================
// PUBLIC ROUTE COMPONENT
// ========================================

export function PublicRoute({
  children,
  redirectTo = '/'
}: PublicRouteProps) {
  const router = useRouter();
  const {
    isAuthenticated,
    isInitialized,
    isLoading,
    shouldRedirectFromAuth,
    isCheckingAuth
  } = useRouteProtection();

  // Redirect authenticated users away from auth pages
  useEffect(() => {
    if (isAuthenticated && isInitialized) {
      console.log('🔄 PUBLIC ROUTE: Redirecting authenticated user');

      // Check if there's a stored redirect path
      const storedRedirect = sessionStorage.getItem('auth:redirectAfterLogin');

      if (storedRedirect) {
        console.log('🔄 REDIRECTING TO STORED PATH:', storedRedirect);
        sessionStorage.removeItem('auth:redirectAfterLogin');
        router.replace(storedRedirect);
      } else {
        console.log('🔄 REDIRECTING TO DEFAULT:', redirectTo);
        router.replace(redirectTo);
      }
    }
  }, [isAuthenticated, isInitialized, redirectTo, router]);

  // Don't render children if authenticated (will redirect)
  if (isAuthenticated) {
    return null;
  }

  // Render public content for unauthenticated users
  return <>{children}</>;
}

// ========================================
// HOC VERSION
// ========================================

/**
 * Higher-order component version of PublicRoute
 * Usage: const PublicComponent = withPublicRoute(MyComponent);
 */
export function withPublicRoute<P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    redirectTo?: string;
  }
) {
  const WrappedComponent = (props: P) => {
    return (
      <PublicRoute redirectTo={options?.redirectTo}>
        <Component {...props} />
      </PublicRoute>
    );
  };

  WrappedComponent.displayName = `withPublicRoute(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

// ========================================
// GUEST ONLY COMPONENT
// ========================================

interface GuestOnlyProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  redirectTo?: string;
}

/**
 * Component that only renders for unauthenticated users
 * Similar to PublicRoute but without automatic redirection
 */
export function GuestOnly({ children, fallback, redirectTo }: GuestOnlyProps) {
  const { isAuthenticated } = useRouteProtection();

  if (isAuthenticated) {
    if (redirectTo) {
      // Trigger redirect if specified
      const router = useRouter();
      useEffect(() => {
        router.replace(redirectTo);
      }, [router, redirectTo]);

      return fallback || null;
    }

    return fallback || null;
  }

  return <>{children}</>;
}

// ========================================
// CONDITIONAL AUTH COMPONENTS
// ========================================

interface ConditionalAuthProps {
  children: React.ReactNode;
  showWhenAuthenticated?: boolean;
  fallback?: React.ReactNode;
}

/**
 * Conditionally render content based on authentication status
 * Useful for navbar items, buttons, etc.
 */
export function ConditionalAuth({
  children,
  showWhenAuthenticated = false,
  fallback
}: ConditionalAuthProps) {
  const { isAuthenticated } = useRouteProtection();

  const shouldShow = showWhenAuthenticated ? isAuthenticated : !isAuthenticated;

  return shouldShow ? <>{children}</> : (fallback || null);
}

// ========================================
// AUTH STATUS INDICATOR
// ========================================

interface AuthStatusIndicatorProps {
  className?: string;
}

/**
 * Visual indicator of authentication status
 * Useful for debugging or admin interfaces
 */
export function AuthStatusIndicator({ className = '' }: AuthStatusIndicatorProps) {
  const { isAuthenticated, isCheckingAuth, isInitialized } = useRouteProtection();

  if (!isInitialized) {
    return (
      <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 ${className}`}>
        <div className="w-2 h-2 bg-gray-400 rounded-full mr-1 animate-pulse"></div>
        Initializing
      </div>
    );
  }

  if (isCheckingAuth) {
    return (
      <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 ${className}`}>
        <div className="w-2 h-2 bg-yellow-400 rounded-full mr-1 animate-pulse"></div>
        Checking
      </div>
    );
  }

  if (isAuthenticated) {
    return (
      <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 ${className}`}>
        <div className="w-2 h-2 bg-green-400 rounded-full mr-1"></div>
        Authenticated
      </div>
    );
  }

  return (
    <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 ${className}`}>
      <div className="w-2 h-2 bg-red-400 rounded-full mr-1"></div>
      Guest
    </div>
  );
}

// ========================================
// EXPORTS
// ========================================

export default PublicRoute;
