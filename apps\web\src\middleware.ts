/**
 * @file apps/web/src/middleware.ts
 * @description Next.js middleware for authentication and route protection
 * Handles automatic redirects and session validation
 */

import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// ========================================
// CONFIGURATION
// ========================================

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://grandline-cms.vercel.app/api';
const COLLECTION_SLUG = 'users'; // Authentication is on users collection, not trainees

// Routes that require authentication
const PROTECTED_ROUTES = [
  '/',
  '/courses',
  '/trending',
  '/music',
  '/gaming',
  '/news',
  '/sports',
  '/subscriptions',
  '/history',
  '/liked-videos',
  '/watch-later',
  '/playlists',
  '/notifications',
  '/portal',
  '/menu',
  '/help',
  '/session-debug',
  '/auth-test',
  '/login-status',
  '/loading-test',
  '/test-home',
  '/shorts'
];

// Routes that should redirect authenticated users
const AUTH_ROUTES = [
  '/signin',
  '/register',
  '/forgot-password'
];

// Routes that are always public (no authentication check)
const PUBLIC_ROUTES = [
  '/api',
  '/_next',
  '/favicon.ico',
  '/calsiter-inc-logo.png',
  '/file.svg',
  '/globe.svg',
  '/next.svg',
  '/vercel.svg',
  '/window.svg'
];

// ========================================
// UTILITY FUNCTIONS
// ========================================

/**
 * Check if a path matches any of the given patterns
 */
function matchesPath(pathname: string, patterns: string[]): boolean {
  return patterns.some(pattern => {
    if (pattern.endsWith('*')) {
      return pathname.startsWith(pattern.slice(0, -1));
    }
    return pathname === pattern || pathname.startsWith(pattern + '/');
  });
}

/**
 * Check authentication status by calling PayloadCMS /me endpoint
 * Only allows users with 'trainee' role
 */
async function checkAuthentication(request: NextRequest): Promise<boolean> {
  try {
    const cookies = request.headers.get('cookie') || '';
    console.log('🔍 MIDDLEWARE: Checking auth for', request.nextUrl.pathname);
    console.log('🍪 MIDDLEWARE: Raw cookies:', cookies);

    // Log specific PayloadCMS cookies
    const payloadToken = request.cookies.get('payload-token');
    console.log('🔑 MIDDLEWARE: payload-token cookie:', payloadToken ? 'EXISTS' : 'MISSING');

    const response = await fetch(`${API_BASE_URL}/${COLLECTION_SLUG}/me`, {
      method: 'GET',
      headers: {
        'Cookie': cookies,
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });

    console.log('📡 MIDDLEWARE: Auth check response status:', response.status);
    console.log('📡 MIDDLEWARE: Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      console.log('❌ MIDDLEWARE: Auth check failed - response not ok');
      const errorText = await response.text();
      console.log('❌ MIDDLEWARE: Error response:', errorText);
      return false;
    }

    const data = await response.json();
    console.log('👤 MIDDLEWARE: Full response data:', data);
    console.log('👤 MIDDLEWARE: User data:', data.user ? { email: data.user.email, role: data.user.role } : 'NO USER');

    // Check if user exists and has trainee role
    const isAuthenticated = data.user !== null && data.user.role === 'trainee';
    console.log('✅ MIDDLEWARE: Authentication result:', isAuthenticated);

    return isAuthenticated;
  } catch (error) {
    console.error('❌ MIDDLEWARE: Authentication check failed:', error);
    return false;
  }
}

/**
 * Create redirect response with proper headers
 */
function createRedirect(url: string, request: NextRequest): NextResponse {
  const response = NextResponse.redirect(new URL(url, request.url));
  
  // Preserve important headers
  response.headers.set('x-middleware-cache', 'no-cache');
  
  return response;
}

// ========================================
// MAIN MIDDLEWARE FUNCTION
// ========================================

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for public routes
  if (matchesPath(pathname, PUBLIC_ROUTES)) {
    return NextResponse.next();
  }

  // Skip middleware for API routes (except auth endpoints)
  if (pathname.startsWith('/api/') && !pathname.startsWith('/api/auth/')) {
    return NextResponse.next();
  }

  try {
    // Check authentication status
    const isAuthenticated = await checkAuthentication(request);

    // Handle protected routes
    if (matchesPath(pathname, PROTECTED_ROUTES)) {
      if (!isAuthenticated) {
        // Store the original URL for redirect after login
        const loginUrl = new URL('/signin', request.url);
        loginUrl.searchParams.set('redirect', pathname);
        
        return createRedirect(loginUrl.toString(), request);
      }
      
      // User is authenticated, allow access
      return NextResponse.next();
    }

    // Handle auth routes (signin, register, etc.)
    if (matchesPath(pathname, AUTH_ROUTES)) {
      if (isAuthenticated) {
        // Check for redirect parameter
        const redirectTo = request.nextUrl.searchParams.get('redirect') || '/';
        return createRedirect(redirectTo, request);
      }
      
      // User is not authenticated, allow access to auth pages
      return NextResponse.next();
    }

    // For all other routes, allow access
    return NextResponse.next();

  } catch (error) {
    console.error('Middleware error:', error);
    
    // On error, allow the request to proceed
    // This prevents the app from breaking if the auth service is down
    return NextResponse.next();
  }
}

// ========================================
// MIDDLEWARE CONFIGURATION
// ========================================

export const config = {
  /*
   * Match all request paths except for the ones starting with:
   * - api (API routes)
   * - _next/static (static files)
   * - _next/image (image optimization files)
   * - favicon.ico (favicon file)
   * - public files (images, etc.)
   */
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};

// ========================================
// ADDITIONAL MIDDLEWARE UTILITIES
// ========================================

/**
 * Middleware for API routes that require authentication
 */
export async function withAuth(handler: (request: NextRequest) => Promise<NextResponse>) {
  return async (request: NextRequest) => {
    const isAuthenticated = await checkAuthentication(request);
    
    if (!isAuthenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    return handler(request);
  };
}

/**
 * Middleware for API routes that require specific roles
 */
export function withRole(roles: string[]) {
  return (handler: (request: NextRequest) => Promise<NextResponse>) => {
    return async (request: NextRequest) => {
      try {
        const response = await fetch(`${API_BASE_URL}/${COLLECTION_SLUG}/me`, {
          method: 'GET',
          headers: {
            'Cookie': request.headers.get('cookie') || '',
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        });

        if (!response.ok) {
          return NextResponse.json(
            { error: 'Unauthorized' },
            { status: 401 }
          );
        }

        const data = await response.json();
        const user = data.user;

        if (!user || !roles.includes(user.role)) {
          return NextResponse.json(
            { error: 'Forbidden' },
            { status: 403 }
          );
        }

        return handler(request);
      } catch (error) {
        return NextResponse.json(
          { error: 'Authentication check failed' },
          { status: 500 }
        );
      }
    };
  };
}
