{"name": "@encreasl/cms-types", "version": "0.1.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": {"types": "./src/index.ts", "default": "./src/index.ts"}, "./api": {"types": "./src/api.ts", "default": "./src/api.ts"}}, "scripts": {"build": "tsc --noEmit", "type-check": "tsc --noEmit", "lint": "eslint . --max-warnings 0"}, "dependencies": {"payload": "3.49.0", "zod": "^4.0.10"}, "devDependencies": {"@encreasl/eslint-config": "workspace:*", "@encreasl/typescript-config": "workspace:*", "@types/node": "^20", "eslint": "^9", "typescript": "^5"}}