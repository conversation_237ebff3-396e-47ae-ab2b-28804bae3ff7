# Firebase Configuration 

NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key_here
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_firebase_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project_id.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_firebase_app_id

# Firebase Admin SDK Configuration (Only 3 variables needed!)
FIREBASE_ADMIN_PROJECT_ID=your_firebase_project_id
FIREBASE_ADMIN_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_firebase_admin_private_key_here\n-----END PRIVATE KEY-----\n"
FIREBASE_ADMIN_CLIENT_EMAIL=firebase-adminsdk-xxxxx@your_project_id.iam.gserviceaccount.com

# Firebase Cloud Messaging Configuration
NEXT_PUBLIC_FIREBASE_VAPID_KEY=your_firebase_vapid_key_here


# Bonsai Elasticsearch Configuration (Free Sandbox Plan)
BONSAI_HOST=https://your_bonsai_host_here:443
BONSAI_USERNAME=your_bonsai_username
BONSAI_PASSWORD=your_bonsai_password

# Cloudinary Configuration
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret
CLOUDINARY_WEBHOOK_SECRET=your_webhook_secret_here



# ========================================
# HYBRID DATABASE ARCHITECTURESUPABASE CONFIGURATION
# ========================================

# Supabase Project Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your_supabase_project_id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# CMS Configuration
ENABLE_SUPABASE_CMS=true

# ========================================
# UPSTASH REDIS CONFIGURATION (Enterprise Caching)
# ========================================

# Upstash Redis Credentials
UPSTASH_REDIS_REST_URL="https://your_upstash_redis_url.upstash.io"
UPSTASH_REDIS_REST_TOKEN="your_upstash_redis_token_here"

# Cache Configuration
ENABLE_REDIS_CACHING=true
REDIS_DEFAULT_TTL=3600

# ========================================
# RESEND EMAIL SERVICE CONFIGURATION
# ========================================

# Resend API Configuration
RESEND_API_KEY=your_resend_api_key_here
NEXT_PUBLIC_RESEND_FROM_EMAIL=<EMAIL>

# Email Configuration
ENABLE_EMAIL_NOTIFICATIONS=true
EMAIL_FROM_NAME=Tap2Go
EMAIL_REPLY_TO=<EMAIL>

# ========================================
# GOOGLE AI STUDIO (GEMINI) CONFIGURATION
# ========================================

# Google AI Studio API Key
# Get your API key from: https://aistudio.google.com/
GOOGLE_AI_API_KEY=your_google_ai_api_key_here

# AI Configuration
ENABLE_AI_FEATURES=true
AI_MODEL_DEFAULT=gemini-1.5-